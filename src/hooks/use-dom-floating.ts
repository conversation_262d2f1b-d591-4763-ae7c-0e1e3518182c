'use client';

import { useCallback, useEffect, useRef } from 'react';
import { flushSync } from 'react-dom';
import { createRoot } from 'react-dom/client';

interface DOMFloatingOptions {
	position?: {
		top?: number;
		bottom?: number;
		left?: number;
		right?: number;
	};
	zIndex?: number;
	className?: string;
	autoShow?: boolean;
}

// Global registry to track floating elements
const floatingRegistry = new Map<
	string,
	{
		element: HTMLDivElement;
		root: any;
		visible: boolean;
	}
>();

export function useDOMFloating(
	id: string,
	content: React.ReactNode,
	options: DOMFloatingOptions = {}
) {
	const isInitialized = useRef(false);

	// Create portal container if it doesn't exist
	useEffect(() => {
		if (typeof window === 'undefined') return;

		let portalContainer = document.getElementById('dom-floating-portal');
		if (!portalContainer) {
			portalContainer = document.createElement('div');
			portalContainer.id = 'dom-floating-portal';
			portalContainer.style.cssText = `
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				pointer-events: none;
				z-index: 1000;
			`;
			document.body.appendChild(portalContainer);
		}

		// Cleanup on page unload to prevent memory leaks
		const handleBeforeUnload = () => {
			floatingRegistry.forEach((entry, id) => {
				// Remove element immediately
				if (entry.element.parentNode) {
					entry.element.remove();
				}

				// Unmount React root synchronously during page unload
				try {
					entry.root.unmount();
				} catch (error) {
					console.warn(`Error unmounting floating UI root ${id}:`, error);
				}
			});
			floatingRegistry.clear();
		};

		window.addEventListener('beforeunload', handleBeforeUnload);
		return () => {
			window.removeEventListener('beforeunload', handleBeforeUnload);
		};
	}, []);

	// Initialize floating element
	useEffect(() => {
		if (typeof window === 'undefined' || isInitialized.current) return;

		const portalContainer = document.getElementById('dom-floating-portal');
		if (!portalContainer) return;

		// Create floating element
		const floatingElement = document.createElement('div');
		floatingElement.id = `floating-${id}`;
		floatingElement.style.cssText = `
			position: fixed;
			${options.position?.top !== undefined ? `top: ${options.position.top}px;` : ''}
			${options.position?.bottom !== undefined ? `bottom: ${options.position.bottom}px;` : ''}
			${options.position?.left !== undefined ? `left: ${options.position.left}px;` : ''}
			${options.position?.right !== undefined ? `right: ${options.position.right}px;` : ''}
			z-index: ${options.zIndex || 1001};
			pointer-events: auto;
			display: none;
		`;

		if (options.className) {
			floatingElement.className = options.className;
		}

		portalContainer.appendChild(floatingElement);

		// Create React root and render content
		const root = createRoot(floatingElement);
		root.render(content as React.ReactElement);

		// Register in global registry
		floatingRegistry.set(id, {
			element: floatingElement,
			root,
			visible: false,
		});

		// Auto-show if requested
		if (options.autoShow) {
			floatingElement.style.display = 'block';
			floatingRegistry.set(id, {
				element: floatingElement,
				root,
				visible: true,
			});
		}

		isInitialized.current = true;

		return () => {
			// Cleanup with proper React scheduling to avoid race conditions
			const entry = floatingRegistry.get(id);
			if (entry) {
				// Remove from registry first
				floatingRegistry.delete(id);

				// Remove element from DOM immediately
				if (entry.element.parentNode) {
					entry.element.remove();
				}

				// Schedule unmount for next tick to avoid race condition
				setTimeout(() => {
					try {
						// Use flushSync to ensure synchronous unmounting when safe
						flushSync(() => {
							entry.root.unmount();
						});
					} catch (error) {
						// Fallback to regular unmount if flushSync fails
						try {
							entry.root.unmount();
						} catch (fallbackError) {
							console.warn('Error unmounting floating UI root:', fallbackError);
						}
					}
				}, 0);
			}
			isInitialized.current = false;
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [id, options.position, options.zIndex, options.className, options.autoShow]);

	// Update content when it changes
	useEffect(() => {
		if (!isInitialized.current) return;

		const entry = floatingRegistry.get(id);
		if (entry) {
			try {
				entry.root.render(content as React.ReactElement);
			} catch (error) {
				console.warn(`Error updating floating UI content for ${id}:`, error);
			}
		}
	}, [id, content]);

	const show = useCallback(() => {
		const entry = floatingRegistry.get(id);
		if (entry && !entry.visible && entry.element.parentNode) {
			entry.element.style.display = 'block';
			floatingRegistry.set(id, { ...entry, visible: true });
		}
	}, [id]);

	const hide = useCallback(() => {
		const entry = floatingRegistry.get(id);
		if (entry && entry.visible && entry.element.parentNode) {
			entry.element.style.display = 'none';
			floatingRegistry.set(id, { ...entry, visible: false });
		}
	}, [id]);

	const toggle = useCallback(() => {
		const entry = floatingRegistry.get(id);
		if (entry) {
			if (entry.visible) {
				hide();
			} else {
				show();
			}
		}
	}, [id, show, hide]);

	const isVisible = floatingRegistry.get(id)?.visible || false;

	return {
		show,
		hide,
		toggle,
		isVisible,
	};
}

// Utility functions
export function hideAllDOMFloating() {
	floatingRegistry.forEach((entry, id) => {
		if (entry.visible) {
			entry.element.style.display = 'none';
			floatingRegistry.set(id, {
				...entry,
				visible: false,
			});
		}
	});
}

export function showAllDOMFloating() {
	floatingRegistry.forEach((entry, id) => {
		if (!entry.visible) {
			entry.element.style.display = 'block';
			floatingRegistry.set(id, {
				...entry,
				visible: true,
			});
		}
	});
}

export function cleanupAllDOMFloating() {
	floatingRegistry.forEach((entry, id) => {
		// Schedule unmount for next tick to avoid race conditions
		setTimeout(() => {
			try {
				entry.root.unmount();
			} catch (error) {
				console.warn(`Error unmounting floating UI root ${id}:`, error);
			}
		}, 0);

		// Remove element immediately
		entry.element.remove();
	});
	floatingRegistry.clear();
}

export function getVisibleDOMFloatingCount() {
	return Array.from(floatingRegistry.values()).filter((entry) => entry.visible).length;
}
