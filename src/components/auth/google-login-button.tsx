'use client';

import { Button, Translate } from '@/components/ui';
import { featureFlags, clientAuthConfig } from '@/config/client.config';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface GoogleLoginButtonProps {
	onSuccess?: () => void;
	onError?: (error: string) => void;
	disabled?: boolean;
}

// Google OAuth types
declare global {
	interface Window {
		google?: {
			accounts: {
				id: {
					initialize: (config: any) => void;
					renderButton: (element: HTMLElement, config: any) => void;
					prompt: () => void;
				};
			};
		};
	}
}

export function GoogleLoginButton({ onSuccess, onError, disabled }: GoogleLoginButtonProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [isGoogleLoaded, setIsGoogleLoaded] = useState(false);
	const router = useRouter();

	// Check if Google login feature is enabled using client config
	const featureEnabled = featureFlags.googleLogin;

	// Load Google Identity Services script
	useEffect(() => {
		if (!featureEnabled) return;

		const loadGoogleScript = () => {
			if (window.google) {
				setIsGoogleLoaded(true);
				return;
			}

			const script = document.createElement('script');
			script.src = 'https://accounts.google.com/gsi/client';
			script.async = true;
			script.defer = true;
			script.onload = () => {
				setIsGoogleLoaded(true);
			};
			script.onerror = () => {
				console.error('Failed to load Google Identity Services');
				onError?.('Failed to load Google login');
			};
			document.head.appendChild(script);
		};

		loadGoogleScript();
	}, [featureEnabled, onError]);

	// Initialize Google Sign-In
	const handleGoogleResponse = useCallback(
		async (response: any) => {
			setIsLoading(true);

			try {
				// Decode the JWT token to get user info
				const payload = JSON.parse(atob(response.credential.split('.')[1]));

				const loginResponse = await fetch('/api/auth/google-login', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						google_id: payload.sub,
						email: payload.email,
						name: payload.name,
					}),
				});

				const data = await loginResponse.json();

				if (!loginResponse.ok) {
					throw new Error(data.error || 'Google login failed');
				}

				onSuccess?.();
				router.push('/');
				router.refresh();
			} catch (error) {
				const errorMessage = error instanceof Error ? error.message : 'Google login failed';
				onError?.(errorMessage);
			} finally {
				setIsLoading(false);
			}
		},
		[onSuccess, onError, router]
	);

	useEffect(() => {
		if (!isGoogleLoaded || !window.google || !featureEnabled) return;

		window.google.accounts.id.initialize({
			client_id: clientAuthConfig.google.clientId,
			callback: handleGoogleResponse,
		});
	}, [isGoogleLoaded, featureEnabled, handleGoogleResponse]);

	const handleGoogleLogin = () => {
		if (!window.google || !isGoogleLoaded) {
			onError?.('Google login not available');
			return;
		}

		window.google.accounts.id.prompt();
	};

	// Don't render if feature is not enabled
	if (!featureEnabled) {
		return null;
	}

	return (
		<Button
			type="button"
			variant="outline"
			className="w-full"
			onClick={handleGoogleLogin}
			disabled={disabled || isLoading || !isGoogleLoaded}
		>
			<svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
				<path
					fill="currentColor"
					d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
				/>
				<path
					fill="currentColor"
					d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
				/>
				<path
					fill="currentColor"
					d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
				/>
				<path
					fill="currentColor"
					d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
				/>
			</svg>
			{isLoading ? (
				<Translate text="auth.google.signing_in" />
			) : (
				<Translate text="auth.google.sign_in" />
			)}
		</Button>
	);
}
