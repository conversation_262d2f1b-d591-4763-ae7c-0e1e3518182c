import {
	BaseRepository,
	BaseRepositoryImpl,
	CollectionRepository,
	CollectionRepositoryImpl,
	CollectionStatsRepository,
	CollectionStatsRepositoryImpl,
	KeywordRepository,
	KeywordRepositoryImpl,
	LastSeenWordRepository,
	LastSeenWordRepositoryImpl,
	UserRepository,
	UserRepositoryImpl,
	WordRepository,
	WordRepositoryImpl,
} from '@/backend/repositories';
import { AuditRepository, AuditRepositoryImpl } from '@/backend/repositories/audit.repository';
import { AuditHelper, createAuditHelper } from '@/backend/utils/audit.helper';
import {
	AdminService,
	AdminServiceImpl,
	AuditService,
	AuditServiceImpl,
	AuthService,
	AuthServiceImpl,
	CollectionService,
	CollectionServiceImpl,
	CollectionStatsService,
	CollectionStatsServiceImpl,
	FeedbackService,
	FeedbackServiceImpl,
	KeywordService,
	KeywordServiceImpl,
	LastSeenWordService,
	LastSeenWordServiceImpl,
	TokenMonitorService,
	UserService,
	UserServiceImpl,
	WordService,
	WordServiceImpl,
} from '@/backend/services';
import { Feedback, PrismaClient } from '@prisma/client';
import { ICacheService } from './cache-init.server';
import { LLMService } from './services/llm.service';

let prismaClient: PrismaClient;
export const getPrismaClient = (): PrismaClient =>
	prismaClient || (prismaClient = new PrismaClient());

let collectionRepository: CollectionRepository;
export const getCollectionRepository = (): CollectionRepository =>
	collectionRepository ||
	(collectionRepository = new CollectionRepositoryImpl(getPrismaClient()));

let keywordRepository: KeywordRepository;
export const getKeywordRepository = (): KeywordRepository =>
	keywordRepository || (keywordRepository = new KeywordRepositoryImpl(getPrismaClient()));

let userRepository: UserRepository;
export const getUserRepository = (): UserRepository =>
	userRepository || (userRepository = new UserRepositoryImpl(getPrismaClient()));

let wordRepository: WordRepository;
export const getWordRepository = (): WordRepository =>
	wordRepository || (wordRepository = new WordRepositoryImpl(getPrismaClient()));

let lastSeenWordRepository: LastSeenWordRepository;
export const getLastSeenWordRepository = (): LastSeenWordRepository =>
	lastSeenWordRepository ||
	(lastSeenWordRepository = new LastSeenWordRepositoryImpl(getPrismaClient()));

let feedbackRepository: BaseRepository<Feedback>;
export const getFeedbackRepository = (): BaseRepository<Feedback> =>
	feedbackRepository ||
	(feedbackRepository = new BaseRepositoryImpl<Feedback>(getPrismaClient().feedback));

let collectionStatsRepository: CollectionStatsRepository;
export const getCollectionStatsRepository = (): CollectionStatsRepository =>
	collectionStatsRepository ||
	(collectionStatsRepository = new CollectionStatsRepositoryImpl(getPrismaClient()));

let auditRepository: AuditRepository;
export const getAuditRepository = (): AuditRepository =>
	auditRepository || (auditRepository = new AuditRepositoryImpl(getPrismaClient()));

let userService: UserService | null = null;
export const getUserService = (): UserService =>
	userService || (userService = new UserServiceImpl(getUserRepository, getAuditHelper));

let authService: AuthService | null = null;
export const getAuthService = (): AuthService =>
	authService || (authService = new AuthServiceImpl(getUserService, getAuditService));

let cacheService: ICacheService | null = null;
export const getCacheService = async (): Promise<ICacheService> => {
	if (!cacheService) {
		cacheService = await getCacheServiceFromInit();
	}
	return cacheService!;
};

let feedbackService: FeedbackService | null = null;
export const getFeedbackService = (): FeedbackService =>
	feedbackService ||
	(feedbackService = new FeedbackServiceImpl(getFeedbackRepository, getAuditHelper));

let lastSeenWordService: LastSeenWordService | null = null;
export const getLastSeenWordService = (): LastSeenWordService =>
	lastSeenWordService ||
	(lastSeenWordService = new LastSeenWordServiceImpl(getLastSeenWordRepository));

let llmService: LLMService | null = null;
export const getLLMService = async (): Promise<LLMService> => {
	if (!llmService) {
		llmService = new LLMService(getWordService);
	}
	return llmService!;
};

let wordService: WordService | null = null;
export const getWordService = (): WordService =>
	wordService ||
	(wordService = new WordServiceImpl(
		getWordRepository,
		getCollectionService,
		getLastSeenWordService,
		getAuditHelper
	));

let collectionService: CollectionService | null = null;
export const getCollectionService = (): CollectionService =>
	collectionService ||
	(collectionService = new CollectionServiceImpl(
		getCollectionRepository,
		getLLMService, // This will be handled by the CollectionService internally
		getWordService,
		getAuditService,
		getAuditHelper
	));

let keywordService: any = null;
export const getKeywordService = (): KeywordService =>
	keywordService || (keywordService = new KeywordServiceImpl(getKeywordRepository));

let collectionStatsService: CollectionStatsService | null = null;
export const getCollectionStatsService = (): CollectionStatsService =>
	collectionStatsService ||
	(collectionStatsService = new CollectionStatsServiceImpl(getCollectionStatsRepository));

let tokenMonitorService: TokenMonitorService | null = null;
export const getTokenMonitorService = (): TokenMonitorService =>
	tokenMonitorService || (tokenMonitorService = new TokenMonitorService());

let auditService: AuditService | null = null;
export const getAuditService = (): AuditService =>
	auditService || (auditService = new AuditServiceImpl(getUserRepository, getAuditRepository));

let auditHelper: AuditHelper | null = null;
export const getAuditHelper = (): AuditHelper =>
	auditHelper || (auditHelper = createAuditHelper(getAuditService));

let adminService: AdminService | null = null;
export const getAdminService = (): AdminService =>
	adminService ||
	(adminService = new AdminServiceImpl(
		getUserService,
		getFeedbackService,
		getFeedbackRepository,
		getCacheService,
		getTokenMonitorService,
		getAuditService,
		getAuditHelper,
		getCollectionService
	));

function getCacheServiceFromInit(): any {
	throw new Error('Function not implemented.');
}
