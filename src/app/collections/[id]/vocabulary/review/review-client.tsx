'use client';

import { WordCard } from '@/app/collections/[id]/components/word-card';
// Removed direct backend API import - now using HTTP request
import { EmptyStateGuidance } from '@/components/onboarding';
import { usePageGuidance } from '@/hooks/use-page-guidance';
import { <PERSON><PERSON>, LoadingSpinner, Translate } from '@/components/ui';
import { useToast } from '@/contexts/toast-context';
import { useTranslation } from '@/contexts';
import { useLoading } from '@/contexts/loading-context';
import { useCollections } from '@/hooks';
import { AnimatePresence, motion } from 'framer-motion';
import { BookOpen, CheckCircle, Zap } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';

const REVIEW_LIMIT = 10;

export function ReviewClient({ params }: { params: { id: string } }) {
	const collectionId = params.id;
	const { currentCollection, loading, error } = useCollections();
	const isLoading = loading.get || loading.setCurrent;
	const { t } = useTranslation();
	const { showSuccess, showError } = useToast();
	const router = useRouter();
	const isActive = true; // Always active in this context

	const { setLoading: setGlobalLoading } = useLoading();
	const {
		currentCollectionWords: wordsToReview,
		loading: { getWordsToReviewWords: getWordsToReviewLoading },
		error: fetchError,
		getCurrentCollectionWordsToReview: getWordsToReview,
	} = useCollections();

	const [currentWordIndex, setCurrentWordIndex] = useState(0);
	const [unlockedWords, setUnlockedWords] = useState<Set<string>>(new Set());
	const [isMarkingSeen, setIsMarkingSeen] = useState(false);
	const [reviewComplete, setReviewComplete] = useState(false);
	const [initialFetchDone, setInitialFetchDone] = useState(false);

	// Page Guidance hook - must be called at the top level
	usePageGuidance({
		titleKey: 'words.guidance.review.title',
		steps: [
			{ key: 'words.guidance.review.step1' },
			{ key: 'words.guidance.review.step2' },
			{ key: 'words.guidance.review.step3' },
		],
		tipKey: 'words.guidance.review.tip',
		defaultOpen: !currentCollection?.word_ids?.length,
	});

	// Reset state when collection changes
	useEffect(() => {
		setInitialFetchDone(false);
		setReviewComplete(false);
		setCurrentWordIndex(0);
		setUnlockedWords(new Set());
	}, [collectionId]);

	// Initial fetch effect - only run once per collection
	useEffect(() => {
		if (isActive && collectionId && !initialFetchDone && !getWordsToReviewLoading) {
			getWordsToReview(REVIEW_LIMIT);
			setInitialFetchDone(true);
		}
	}, [collectionId, isActive, initialFetchDone, getWordsToReviewLoading, getWordsToReview]);

	// Review completion check effect
	useEffect(() => {
		if (initialFetchDone && wordsToReview.length > 0) {
			if (currentWordIndex >= wordsToReview.length) {
				setReviewComplete(true);
			} else {
				setReviewComplete(false);
			}
		}
	}, [initialFetchDone, wordsToReview.length, currentWordIndex]);

	useEffect(() => {
		if (isActive) {
			setGlobalLoading(getWordsToReviewLoading || isMarkingSeen);
		} else {
			if (getWordsToReviewLoading || isMarkingSeen) {
				setGlobalLoading(false);
			}
		}
	}, [getWordsToReviewLoading, isMarkingSeen, setGlobalLoading, isActive]);

	const handleToggleVietnamese = useCallback((wordId: string) => {
		setUnlockedWords((prev) => {
			const newSet = new Set(prev);
			if (newSet.has(wordId)) {
				newSet.delete(wordId);
			} else {
				newSet.add(wordId);
			}
			return newSet;
		});
	}, []);

	const handleNextWord = useCallback(async () => {
		if (currentWordIndex >= wordsToReview.length) return;

		const currentWord = wordsToReview[currentWordIndex];
		setIsMarkingSeen(true);
		try {
			const response = await fetch('/api/last-seen-word', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ wordId: currentWord.id }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to mark word as seen');
			}
		} catch (error) {
			console.error('Failed to mark word as seen:', error);
			showError(new Error(t('review.mark_seen_error_title')));
		} finally {
			setIsMarkingSeen(false);
		}

		if (currentWordIndex < wordsToReview.length - 1) {
			setCurrentWordIndex((prev) => prev + 1);
			setUnlockedWords(new Set());
		} else {
			setReviewComplete(true);
			showError(new Error(t('review.session_complete_title')));
		}
	}, [currentWordIndex, wordsToReview, showError, t]);

	const handleRestartReview = useCallback(() => {
		if (collectionId) {
			setReviewComplete(false);
			setCurrentWordIndex(0);
			setUnlockedWords(new Set());
			setInitialFetchDone(false);
			getWordsToReview(REVIEW_LIMIT);
		}
	}, [collectionId, getWordsToReview]);

	const handleGenerateWords = useCallback(() => {
		if (collectionId) {
			router.push(`/collections/${collectionId}/vocabulary/generate`);
		}
	}, [collectionId, router]);

	const currentWordToDisplay = useMemo(() => {
		if (
			isActive &&
			!reviewComplete &&
			wordsToReview &&
			wordsToReview.length > 0 &&
			currentWordIndex < wordsToReview.length
		) {
			return wordsToReview[currentWordIndex];
		}
		return null;
	}, [wordsToReview, currentWordIndex, reviewComplete, isActive]);

	const wordCountText = useMemo(() => {
		return isActive && wordsToReview?.length > 0 && !reviewComplete && currentWordToDisplay
			? `(${currentWordIndex + 1}/${wordsToReview.length})`
			: '';
	}, [wordsToReview, currentWordIndex, reviewComplete, currentWordToDisplay, isActive]);

	if (!initialFetchDone && !isActive) {
		return (
			<div className="py-4 text-center text-muted-foreground">
				{/* Optionally, a message like "Activate tab to load review" */}
			</div>
		);
	}

	if (getWordsToReviewLoading && wordsToReview.length === 0 && isActive) {
		// Show loading only if active
		return <PracticeSessionSkeleton type="review" />;
	}

	if (fetchError && isActive) {
		return (
			<div className="container mx-auto py-8 text-center">
				<p className="text-destructive mb-4">
					{t('review.fetch_error_title')}:{' '}
					{fetchError?.message || t('review.unknown_error')}
				</p>
				<Button onClick={handleRestartReview} variant="outline">
					<Translate text="review.try_refetch_btn" />
				</Button>
			</div>
		);
	}

	if (!currentCollection) return null;

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background">
			<div className="max-w-6xl mx-auto space-y-8 py-8">
				<header className="text-center space-y-4">
					<h1 className="text-4xl font-bold text-primary dark:text-primary">
						<Translate text="words.review_words" />
					</h1>
					<p className="text-muted-foreground dark:text-muted-foreground text-lg">
						<Translate text="words.review_description" />
					</p>
				</header>

				<div className="py-4">
					<div className="flex items-center justify-end mb-4 min-h-[24px]">
						{wordCountText && (
							<div className="text-muted-foreground text-sm sm:text-base">
								{wordCountText}
							</div>
						)}
					</div>
					<AnimatePresence mode="wait">
						{reviewComplete && isActive ? (
							<motion.div
								key="complete"
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								exit={{ opacity: 0, y: -20 }}
								className="text-center py-10"
							>
								<CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
								<h2 className="text-2xl font-semibold mb-4">
									<Translate text="review.session_complete_title" />
								</h2>
								<p className="text-muted-foreground mb-6">
									<Translate text="review.all_words_reviewed_desc" />
								</p>
								<div className="flex flex-wrap gap-4 justify-center">
									<Button onClick={handleGenerateWords} variant="outline">
										<Zap className="mr-2 h-4 w-4" />
										<Translate text="words.generate_words" />
									</Button>
									<Button onClick={handleRestartReview}>
										<Translate text="review.review_more_btn" />
									</Button>
								</div>
							</motion.div>
						) : currentWordToDisplay && isActive ? (
							<motion.div
								key={currentWordToDisplay.id}
								initial={{ opacity: 0, x: 50 }}
								animate={{ opacity: 1, x: 0 }}
								exit={{ opacity: 0, x: -50 }}
								transition={{ duration: 0.3 }}
								className="mx-auto"
							>
								<WordCard
									word={currentWordToDisplay}
									isReviewMode={true}
									showSourceLanguage={unlockedWords.has(currentWordToDisplay.id)}
									onToggleTargetLanguage={() =>
										handleToggleVietnamese(currentWordToDisplay.id)
									}
									className="shadow-xl"
									defaultExpanded={true}
									sourceLanguage={currentCollection?.source_language}
									targetLanguage={currentCollection?.target_language}
								/>
								<div className="mt-6 flex justify-end gap-4">
									<Button
										onClick={handleNextWord}
										disabled={isMarkingSeen}
										size="lg"
										className="min-w-[180px] bg-green-600 hover:bg-green-700 text-white"
									>
										{isMarkingSeen ? (
											<LoadingSpinner size="sm" />
										) : (
											<>
												<CheckCircle className="mr-2 h-5 w-5" />
												<Translate text="review.mark_seen_next_btn" />
											</>
										)}
									</Button>
									<Button
										variant="outline"
										onClick={() => {
											if (currentWordIndex < wordsToReview.length - 1) {
												setCurrentWordIndex((prev) => prev + 1);
												setUnlockedWords(new Set());
											} else {
												setReviewComplete(true);
												showError(
													new Error(t('review.session_complete_title'))
												);
											}
										}}
										disabled={reviewComplete}
										size="lg"
										className="min-w-[120px]"
									>
										<Translate text="review.skip_btn" />
									</Button>
								</div>
							</motion.div>
						) : isActive &&
						  initialFetchDone &&
						  !getWordsToReviewLoading &&
						  wordsToReview.length === 0 ? (
							<EmptyStateGuidance
								titleKey="words.empty.review.title"
								descriptionKey="words.empty.review.description"
								actionKey="words.empty.review.action"
								actionHref={`/collections/${currentCollection.id}/vocabulary/generate`}
								icon={BookOpen}
							/>
						) : null}
					</AnimatePresence>
				</div>
			</div>
		</div>
	);
}
